#!/usr/bin/env python3
"""
Kibana Query Script
Query Kibana/Elasticsearch logs by RequestUID
"""

import requests
import json
import sys
import argparse
from datetime import datetime
import urllib3

# Suppress SSL warnings for internal corporate endpoints
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def query_kibana(request_uid):
    """Query Kibana for a specific RequestUID"""
    
    # Endpoint and headers
    url = "https://eapi.kb.ceceop.scb.co.th:9243/api/console/proxy"
    params = {
        'path': 'gw-prd/_search',
        'method': 'POST'
    }
    
    headers = {
        'Content-Type': 'application/json',
        'kbn-xsrf': 'true',
        'Authorization': 'Basic ZWNlLWVhcGktYWQtdXNlcjp0RiFlU3EtdWdhUEpAMw=='
    }
    
    # Query payload
    payload = {
        "from": 0,
        "size": 100,
        "_source": [
            "jsonResp.RequestUID",
            "jsonResp.Latency",
            "jsonResp.routingCode", 
            "jsonResp.routingUrl",
            "@timestamp"
        ],
        "query": {
            "bool": {
                "must": [
                    {
                        "match": {
                            "jsonResp.RequestUID": request_uid
                        }
                    },
                    {
                        "match": {
                            "jsonResp.Level": "AUDIT"
                        }
                    }
                ]
            }
        }
    }
    
    try:
        print(f"🔍 Searching for RequestUID: {request_uid}")
        
        response = requests.post(url, params=params, headers=headers, json=payload, verify=False)
        response.raise_for_status()
        
        data = response.json()
        
        print(f"✅ SUCCESS: Query executed successfully!")
        print(f"📊 Found {data['hits']['total']['value']} record(s)")
        print(f"⏱️  Query took: {data['took']}ms")
        
        if data['hits']['total']['value'] > 0:
            print("\n" + "="*80)
            print("RESULTS:")
            print("="*80)
            
            total_latency = 0
            latency_values = []
            valid_records = 0
            
            for i, hit in enumerate(data['hits']['hits'], 1):
                source = hit['_source']
                
                # Debug: Show available fields
                print(f"\n🔍 DEBUG - Available fields in record {i}:")
                if 'jsonResp' in source:
                    print(f"   jsonResp keys: {list(source['jsonResp'].keys())}")
                else:
                    print(f"   Top-level keys: {list(source.keys())}")
                
                print(f"\n📋 Record {i}:")
                
                # Try different field name variations
                request_uid = (source.get('jsonResp', {}).get('RequestUID') or 
                              source.get('jsonResp', {}).get('requestUID') or
                              source.get('RequestUID') or
                              source.get('requestUID') or 'N/A')
                
                latency = (source.get('jsonResp', {}).get('Latency') or 
                          source.get('jsonResp', {}).get('latency') or
                          source.get('Latency') or
                          source.get('latency') or 'N/A')
                
                routing_code = (source.get('jsonResp', {}).get('routingCode') or 
                               source.get('jsonResp', {}).get('RoutingCode') or
                               source.get('routingCode') or
                               source.get('RoutingCode') or 'N/A')
                
                routing_url = (source.get('jsonResp', {}).get('routingUrl') or 
                              source.get('jsonResp', {}).get('RoutingUrl') or
                              source.get('routingUrl') or
                              source.get('RoutingUrl') or 'N/A')
                
                print(f"   🆔 RequestUID: {request_uid}")
                print(f"   ⏱️  Latency: {latency}ms")
                print(f"   🔢 Routing Code: {routing_code}")
                print(f"   🌐 Routing URL: {routing_url}")
                print(f"   📅 Timestamp: {source.get('@timestamp', 'N/A')}")
                print(f"   📁 Index: {hit['_index']}")
                
                # Collect latency for total calculation
                if latency != 'N/A':
                    try:
                        latency_num = float(str(latency).replace('ms', ''))
                        latency_values.append(latency_num)
                        total_latency += latency_num
                        valid_records += 1
                    except (ValueError, TypeError):
                        print(f"   ⚠️  Warning: Could not parse latency value: {latency}")
                
                # Show raw jsonResp for debugging
                if 'jsonResp' in source:
                    print(f"\n🔧 DEBUG - Raw jsonResp content:")
                    print(json.dumps(source['jsonResp'], indent=4))
            
            # Summary and timeout analysis
            print("\n" + "="*80)
            print("TRANSACTION SUMMARY:")
            print("="*80)
            print(f"📊 Total Records: {data['hits']['total']['value']}")
            print(f"⏱️  Records with Valid Latency: {valid_records}")
            
            if valid_records > 0:
                print(f"🔢 Individual Latencies: {latency_values}")
                print(f"⏱️  Total Latency: {total_latency}ms")
                print(f"📊 Average Latency: {total_latency/valid_records:.2f}ms")
                
                # Timeout analysis
                print(f"\n🚨 TIMEOUT ANALYSIS:")
                if total_latency < 0:
                    print(f"   🔴 ABNORMAL: {total_latency}ms < 0ms")
                    print(f"   🔍 CONNECTION ISSUE: Negative latency detected - needs further investigation")
                elif total_latency > 11000:  # 11 seconds
                    print(f"   ❌ TIMEOUT: {total_latency}ms > 11,000ms (11s)")
                    print(f"   💡 RECOMMENDATION: Transaction has timed out")
                else:
                    print(f"   ✅ NORMAL: {total_latency}ms is within acceptable range (0-11,000ms)")
                    if total_latency > 8000:  # Close to timeout
                        print(f"   ⚠️  NOTE: Close to timeout threshold ({total_latency}ms / 11,000ms)")
                
                # Composite request analysis
                if data['hits']['total']['value'] > 1:
                    print(f"\n🔗 COMPOSITE REQUEST DETECTED:")
                    print(f"   📋 This is a composite request with {data['hits']['total']['value']} parts")
                    print(f"   ⏱️  Total execution time: {total_latency}ms")
                    print(f"   📊 Average per request: {total_latency/valid_records:.2f}ms")
            else:
                print(f"❌ No valid latency data found in any records")
        else:
            print(f"\n❌ No records found for RequestUID: {request_uid}")
            
        return data
        
    except requests.exceptions.RequestException as e:
        print(f"❌ ERROR: Request failed")
        print(f"   Error: {str(e)}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"   Status Code: {e.response.status_code}")
            print(f"   Response: {e.response.text}")
        return None
    except Exception as e:
        print(f"❌ ERROR: Unexpected error occurred")
        print(f"   Error: {str(e)}")
        return None

def main():
    parser = argparse.ArgumentParser(description='Query Kibana logs by RequestUID')
    parser.add_argument('request_uid', nargs='?', default='YOUR_REQUEST_UID_HERE',
                      help='RequestUID to search for')
    parser.add_argument('--json', action='store_true',
                      help='Output full JSON response')
    parser.add_argument('--save', type=str,
                      help='Save results to file (specify filename)')
    
    args = parser.parse_args()
    
    if args.request_uid == 'YOUR_REQUEST_UID_HERE':
        print("⚠️  Please provide a RequestUID:")
        print("   python kibana_query.py YOUR_REQUEST_UID")
        print("   python kibana_query.py 5f18d1d99aaf4d0d9772e5b1c89c1fe2")
        sys.exit(1)
    
    # Execute query
    result = query_kibana(args.request_uid)
    
    if result and args.json:
        print("\n" + "="*80)
        print("FULL JSON RESPONSE:")
        print("="*80)
        print(json.dumps(result, indent=2))
    
    if result and args.save:
        try:
            with open(args.save, 'w') as f:
                json.dump(result, f, indent=2)
            print(f"💾 Results saved to: {args.save}")
        except Exception as e:
            print(f"❌ Failed to save file: {str(e)}")

if __name__ == "__main__":
    main() 