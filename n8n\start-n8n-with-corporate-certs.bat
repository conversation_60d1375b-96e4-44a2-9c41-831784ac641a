@echo off
echo Starting n8n with Corporate SSL Certificates...
echo This uses your organization's certificates for secure HTTPS requests.
echo.

REM Get the current directory
set CURRENT_DIR=%~dp0

REM Set Node.js to use corporate certificates
set NODE_EXTRA_CA_CERTS=%CURRENT_DIR%corporate-ca-bundle.pem

REM Display certificate info
echo Using certificate bundle: %NODE_EXTRA_CA_CERTS%
echo.

REM Verify certificate file exists
if not exist "%NODE_EXTRA_CA_CERTS%" (
    echo ERROR: Certificate bundle not found!
    echo Please ensure corporate-ca-bundle.pem exists in the current directory.
    pause
    exit /b 1
)

echo Certificate bundle found. Starting n8n...
echo.

REM Start n8n with corporate certificates
npx n8n

echo.
echo n8n has been stopped.
pause
