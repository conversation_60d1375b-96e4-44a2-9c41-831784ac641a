#!/usr/bin/env python3
"""
JIRA Request UID Extractor using OpenTyphoon.ai API

This script processes JIRA tickets from the "API Implementation - Production" (AIP) project
to extract Request UID (RQUID) values from Thai language text using OpenTyphoon.ai API.

Requirements:
- JIRA_API_TOKEN environment variable
- OPENTYPHOON_API_KEY environment variable
- Python packages: jira, openai, python-dotenv, pandas, tqdm

Author: AI Assistant
Date: 2025-06-26
"""

import os
import csv
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import re

# Third-party imports
from jira import JIRA
from openai import OpenAI
from dotenv import load_dotenv
import pandas as pd
from tqdm import tqdm

# Load environment variables
load_dotenv()

# Configuration
JIRA_DOMAIN = "https://scbjira.atlassian.net"
EMAIL = "<EMAIL>"
JIRA_API_TOKEN = os.getenv('JIRA_API_TOKEN')
OPENTYPHOON_API_KEY = os.getenv('OPENTYPHOON_API_KEY')

# OpenTyphoon.ai configuration
OPENTYPHOON_BASE_URL = "https://api.opentyphoon.ai/v1"
OPENTYPHOON_MODEL = "typhoon-v2-70b-instruct"

# Processing configuration
BATCH_SIZE = 5  # Process tickets in batches to avoid rate limits
DELAY_BETWEEN_BATCHES = 2  # Seconds to wait between batches
MAX_RETRIES = 3  # Maximum retries for API calls

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('jira_rquid_extraction.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class JiraRQUIDExtractor:
    """Main class for extracting Request UIDs from JIRA tickets using OpenTyphoon.ai"""
    
    def __init__(self):
        """Initialize the extractor with JIRA and OpenTyphoon.ai clients"""
        self.jira_client = None
        self.typhoon_client = None
        self.results = []
        
        # Validate environment variables
        if not JIRA_API_TOKEN:
            raise ValueError("JIRA_API_TOKEN environment variable not set!")
        if not OPENTYPHOON_API_KEY:
            raise ValueError("OPENTYPHOON_API_KEY environment variable not set!")
        
        self._initialize_clients()
    
    def _initialize_clients(self):
        """Initialize JIRA and OpenTyphoon.ai clients"""
        try:
            # Initialize JIRA client
            self.jira_client = JIRA(
                server=JIRA_DOMAIN,
                basic_auth=(EMAIL, JIRA_API_TOKEN)
            )
            logger.info("✅ Successfully connected to JIRA!")
            
            # Initialize OpenTyphoon.ai client
            self.typhoon_client = OpenAI(
                api_key=OPENTYPHOON_API_KEY,
                base_url=OPENTYPHOON_BASE_URL
            )
            logger.info("✅ Successfully initialized OpenTyphoon.ai client!")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize clients: {e}")
            raise
    
    def get_open_aip_tickets(self) -> List:
        """Fetch all open tickets from AIP project"""
        try:
            jql_query = 'project = AIP AND status = Open'
            issues = self.jira_client.search_issues(
                jql_query, 
                maxResults=100,  # Adjust as needed
                expand='description'  # Ensure we get full description
            )
            logger.info(f"📋 Found {len(issues)} open issues in AIP project")
            return issues
        except Exception as e:
            logger.error(f"❌ Failed to fetch JIRA tickets: {e}")
            raise
    
    def extract_rquid_with_typhoon(self, text: str, ticket_id: str) -> Tuple[Optional[str], float, str]:
        """
        Extract Request UID from text using OpenTyphoon.ai API
        
        Returns:
            Tuple of (extracted_rquid, confidence, status)
        """
        if not text or not text.strip():
            return None, 0.0, "Empty text"
        
        # Create a focused prompt for RQUID extraction
        system_prompt = """คุณเป็นผู้เชี่ยวชาญในการสกัดข้อมูล Request UID (RQUID) จากข้อความภาษาไทย
        
งานของคุณคือ:
1. ค้นหา Request UID ในรูปแบบต่างๆ เช่น:
   - requestUID = [ตัวเลข]
   - RQUID: [ตัวเลข]  
   - Request UID: [ตัวเลข]
   - requestId = [ตัวเลข]
   - หรือรูปแบบอื่นที่คล้ายกัน

2. ตอบกลับในรูปแบบ JSON เท่านั้น:
   {
     "rquid": "ตัวเลข Request UID ที่พบ หรือ null ถ้าไม่พบ",
     "confidence": ตัวเลขระหว่าง 0.0-1.0 แสดงความมั่นใจ,
     "found_pattern": "รูปแบบที่พบ เช่น 'requestUID = 123' หรือ null"
   }

3. ถ้าไม่พบ Request UID ให้ตอบ:
   {
     "rquid": null,
     "confidence": 0.0,
     "found_pattern": null
   }"""
        
        user_prompt = f"กรุณาสกัด Request UID จากข้อความนี้:\n\n{text}"
        
        for attempt in range(MAX_RETRIES):
            try:
                response = self.typhoon_client.chat.completions.create(
                    model=OPENTYPHOON_MODEL,
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ],
                    temperature=0.1,  # Low temperature for consistent extraction
                    max_tokens=200
                )
                
                result_text = response.choices[0].message.content.strip()
                logger.debug(f"OpenTyphoon.ai response for {ticket_id}: {result_text}")
                
                # Parse JSON response
                try:
                    result = json.loads(result_text)
                    rquid = result.get('rquid')
                    confidence = float(result.get('confidence', 0.0))
                    status = f"Success - Pattern: {result.get('found_pattern', 'N/A')}"
                    
                    return rquid, confidence, status
                    
                except json.JSONDecodeError:
                    # Fallback: try to extract using regex if JSON parsing fails
                    logger.warning(f"JSON parsing failed for {ticket_id}, trying regex fallback")
                    return self._regex_fallback_extraction(result_text, ticket_id)
                
            except Exception as e:
                logger.warning(f"Attempt {attempt + 1} failed for {ticket_id}: {e}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(1)  # Wait before retry
                else:
                    return None, 0.0, f"API Error: {str(e)}"
        
        return None, 0.0, "Max retries exceeded"
    
    def _regex_fallback_extraction(self, text: str, ticket_id: str) -> Tuple[Optional[str], float, str]:
        """Fallback regex-based RQUID extraction"""
        patterns = [
            r'requestUID\s*[=:]\s*(\d+)',
            r'RQUID\s*[=:]\s*(\d+)',
            r'Request\s*UID\s*[=:]\s*(\d+)',
            r'requestId\s*[=:]\s*(\d+)',
            r'(?:request|req)\s*(?:uid|id)\s*[=:]\s*(\d+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                rquid = match.group(1)
                logger.info(f"Regex fallback found RQUID {rquid} for {ticket_id}")
                return rquid, 0.8, f"Regex extraction - Pattern: {pattern}"
        
        return None, 0.0, "No RQUID found (regex fallback)"

    def process_ticket(self, issue) -> Dict:
        """Process a single JIRA ticket to extract RQUID"""
        ticket_id = issue.key
        summary = issue.fields.summary or ""
        description = getattr(issue.fields, 'description', '') or ""

        # Combine summary and description for analysis
        full_text = f"{summary}\n\n{description}".strip()

        logger.info(f"🎫 Processing ticket: {ticket_id}")

        # Extract RQUID using OpenTyphoon.ai
        rquid, confidence, status = self.extract_rquid_with_typhoon(full_text, ticket_id)

        result = {
            'Ticket_ID': ticket_id,
            'Summary': summary,
            'Request_UID': rquid,
            'Extraction_Confidence': confidence,
            'Processing_Status': status,
            'Full_Text_Length': len(full_text),
            'Processed_At': datetime.now().isoformat()
        }

        # Log result
        if rquid:
            logger.info(f"✅ {ticket_id}: Found RQUID {rquid} (confidence: {confidence:.2f})")
        else:
            logger.info(f"❌ {ticket_id}: No RQUID found - {status}")

        return result

    def process_all_tickets(self) -> List[Dict]:
        """Process all open AIP tickets"""
        logger.info("🚀 Starting RQUID extraction process...")

        # Get all open tickets
        tickets = self.get_open_aip_tickets()

        if not tickets:
            logger.warning("No open tickets found in AIP project")
            return []

        # Process tickets in batches
        all_results = []
        total_batches = (len(tickets) + BATCH_SIZE - 1) // BATCH_SIZE

        for batch_num in range(total_batches):
            start_idx = batch_num * BATCH_SIZE
            end_idx = min(start_idx + BATCH_SIZE, len(tickets))
            batch_tickets = tickets[start_idx:end_idx]

            logger.info(f"📦 Processing batch {batch_num + 1}/{total_batches} ({len(batch_tickets)} tickets)")

            # Process tickets in current batch
            batch_results = []
            for ticket in tqdm(batch_tickets, desc=f"Batch {batch_num + 1}"):
                try:
                    result = self.process_ticket(ticket)
                    batch_results.append(result)
                    self.results.append(result)
                except Exception as e:
                    logger.error(f"❌ Error processing {ticket.key}: {e}")
                    error_result = {
                        'Ticket_ID': ticket.key,
                        'Summary': getattr(ticket.fields, 'summary', 'N/A'),
                        'Request_UID': None,
                        'Extraction_Confidence': 0.0,
                        'Processing_Status': f"Error: {str(e)}",
                        'Full_Text_Length': 0,
                        'Processed_At': datetime.now().isoformat()
                    }
                    batch_results.append(error_result)
                    self.results.append(error_result)

            all_results.extend(batch_results)

            # Wait between batches to respect rate limits
            if batch_num < total_batches - 1:
                logger.info(f"⏳ Waiting {DELAY_BETWEEN_BATCHES} seconds before next batch...")
                time.sleep(DELAY_BETWEEN_BATCHES)

        return all_results

    def save_results_to_csv(self, filename: str = None):
        """Save results to CSV file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"jira_rquid_extraction_results_{timestamp}.csv"

        try:
            df = pd.DataFrame(self.results)
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            logger.info(f"💾 Results saved to: {filename}")

            # Print summary statistics
            total_tickets = len(self.results)
            found_rquids = len([r for r in self.results if r['Request_UID']])
            success_rate = (found_rquids / total_tickets * 100) if total_tickets > 0 else 0

            logger.info(f"📊 Summary Statistics:")
            logger.info(f"   Total tickets processed: {total_tickets}")
            logger.info(f"   RQUIDs found: {found_rquids}")
            logger.info(f"   Success rate: {success_rate:.1f}%")

        except Exception as e:
            logger.error(f"❌ Failed to save results to CSV: {e}")
            raise

    def print_results_summary(self):
        """Print a formatted summary of results"""
        if not self.results:
            print("No results to display.")
            return

        print("\n" + "="*80)
        print("JIRA REQUEST UID EXTRACTION RESULTS")
        print("="*80)

        for result in self.results:
            print(f"\nTicket ID: {result['Ticket_ID']}")
            print(f"Summary: {result['Summary'][:100]}{'...' if len(result['Summary']) > 100 else ''}")

            if result['Request_UID']:
                print(f"Request UID: {result['Request_UID']}")
                print(f"Confidence: {result['Extraction_Confidence']:.2f}")
                print("Status: ✅ Successfully extracted")
            else:
                print("Request UID: Not found")
                print(f"Status: ❌ {result['Processing_Status']}")

            print("-" * 40)


def main():
    """Main function to run the RQUID extraction process"""
    try:
        # Initialize extractor
        extractor = JiraRQUIDExtractor()

        # Process all tickets
        results = extractor.process_all_tickets()

        # Save results to CSV
        extractor.save_results_to_csv()

        # Print summary
        extractor.print_results_summary()

        logger.info("🎉 RQUID extraction process completed successfully!")

    except KeyboardInterrupt:
        logger.info("⏹️ Process interrupted by user")
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        raise


if __name__ == "__main__":
    main()
