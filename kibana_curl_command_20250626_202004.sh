#!/bin/bash
# Kibana query for RQUID: 78148764314453026234
# Generated: 2025-06-26T20:20:04.369057

curl --location --request POST 'https://eapi.kb.ceceop.scb.co.th:9243/api/console/proxy?path=gw-prd%2F_search&method=POST' \
--header 'Content-Type: application/json' \
--header 'kbn-xsrf: true' \
--header 'Authorization: Basic ZWNlLWVhcGktYWQtdXNlcjp0RiFlU3EtdWdhUEpAMw==' \
--data-raw '{"from":0,"size":100,"_source":["jsonResp.RequestUID","jsonResp.requestUrl","jsonResp.routingUrl","jsonResp.routingCode","jsonResp.serviceName","jsonResp.Latency","@timestamp"],"query":{"bool":{"must":[{"match":{"jsonResp.RequestUID":"PYMDFd247b36487491846a81711be2cc"}},{"match":{"jsonResp.Level":"AUDIT"}}]}}}'