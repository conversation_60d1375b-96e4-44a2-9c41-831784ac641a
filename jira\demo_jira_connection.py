#!/usr/bin/env python3
"""
Demo script to test JIRA connection and show sample ticket data

This script demonstrates:
1. JIRA connection using existing credentials
2. Fetching open AIP tickets
3. Showing what data would be processed for RQUID extraction
4. Mock RQUID extraction to demonstrate the workflow

Run this to verify JIRA connectivity before setting up OpenTyphoon.ai API key.
"""

import os
import re
from datetime import datetime
from jira import JIRA
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# JIRA Configuration
JIRA_DOMAIN = "https://scbjira.atlassian.net"
EMAIL = "<EMAIL>"
JIRA_API_TOKEN = os.getenv('JIRA_API_TOKEN')

def mock_rquid_extraction(text: str) -> tuple:
    """
    Mock RQUID extraction using regex patterns
    This simulates what OpenTyphoon.ai would do but using simple regex
    """
    if not text or not text.strip():
        return None, 0.0, "Empty text"
    
    # Common RQUID patterns
    patterns = [
        (r'requestUID\s*[=:]\s*(\d+)', 'requestUID pattern'),
        (r'RQUID\s*[=:]\s*(\d+)', 'RQUID pattern'),
        (r'Request\s*UID\s*[=:]\s*(\d+)', 'Request UID pattern'),
        (r'requestId\s*[=:]\s*(\d+)', 'requestId pattern'),
        (r'(?:request|req)\s*(?:uid|id)\s*[=:]\s*(\d+)', 'Generic request ID pattern'),
    ]
    
    for pattern, pattern_name in patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            rquid = match.group(1)
            confidence = 0.85  # Mock confidence score
            return rquid, confidence, f"Found using {pattern_name}"
    
    return None, 0.0, "No RQUID pattern found"

def test_jira_connection():
    """Test JIRA connection and fetch sample tickets"""
    
    if not JIRA_API_TOKEN:
        print("❌ JIRA_API_TOKEN not set in .env file")
        return False
    
    try:
        # Connect to JIRA
        jira = JIRA(
            server=JIRA_DOMAIN,
            basic_auth=(EMAIL, JIRA_API_TOKEN)
        )
        print("✅ Successfully connected to JIRA!")
        
        # Fetch open AIP tickets
        jql_query = 'project = AIP AND status = Open'
        issues = jira.search_issues(jql_query, maxResults=10, expand='description')
        
        print(f"\n📋 Found {len(issues)} open issues in AIP project (showing first 10)")
        
        if not issues:
            print("No open tickets found.")
            return True
        
        print("\n" + "="*80)
        print("SAMPLE TICKET ANALYSIS")
        print("="*80)
        
        found_rquids = 0
        
        for i, issue in enumerate(issues, 1):
            ticket_id = issue.key
            summary = issue.fields.summary or ""
            description = getattr(issue.fields, 'description', '') or ""
            
            # Combine summary and description
            full_text = f"{summary}\n\n{description}".strip()
            
            print(f"\n🎫 Ticket {i}: {ticket_id}")
            print(f"Summary: {summary}")
            print(f"Description length: {len(description)} characters")
            print(f"Full text length: {len(full_text)} characters")
            
            # Mock RQUID extraction
            rquid, confidence, status = mock_rquid_extraction(full_text)
            
            if rquid:
                print(f"✅ MOCK EXTRACTION - Request UID: {rquid}")
                print(f"   Confidence: {confidence:.2f}")
                print(f"   Method: {status}")
                found_rquids += 1
            else:
                print(f"❌ MOCK EXTRACTION - {status}")
            
            # Show a snippet of the text for context
            if full_text:
                snippet = full_text[:200] + "..." if len(full_text) > 200 else full_text
                print(f"Text snippet: {snippet}")
            
            print("-" * 60)
        
        # Summary
        success_rate = (found_rquids / len(issues) * 100) if issues else 0
        print(f"\n📊 MOCK EXTRACTION SUMMARY:")
        print(f"   Total tickets analyzed: {len(issues)}")
        print(f"   RQUIDs found (mock): {found_rquids}")
        print(f"   Success rate (mock): {success_rate:.1f}%")
        
        print(f"\n💡 This demonstrates the workflow. With OpenTyphoon.ai API:")
        print(f"   - More accurate Thai language understanding")
        print(f"   - Better handling of complex text patterns")
        print(f"   - Confidence scores from AI model")
        print(f"   - Ability to understand context and variations")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to connect to JIRA: {e}")
        return False

def show_setup_instructions():
    """Show setup instructions for OpenTyphoon.ai"""
    print("\n" + "="*80)
    print("NEXT STEPS - OPENTYPHOON.AI SETUP")
    print("="*80)
    print("\n1. Sign up for OpenTyphoon.ai:")
    print("   - Visit: https://opentyphoon.ai")
    print("   - Create an account")
    print("   - Navigate to API Keys section")
    print("   - Create a new API key")
    
    print("\n2. Update your .env file:")
    print("   - Open .env file in this directory")
    print("   - Replace 'your_opentyphoon_api_key_here' with your actual API key")
    print("   - Save the file")
    
    print("\n3. Test the setup:")
    print("   - Run: python test_rquid_extractor.py")
    print("   - This will test the OpenTyphoon.ai connection")
    
    print("\n4. Run the full extraction:")
    print("   - Run: python jira_rquid_extractor.py")
    print("   - Check the generated CSV file for results")
    
    print("\n📝 Files you'll get:")
    print("   - CSV report with extraction results")
    print("   - Log file with detailed processing information")

def main():
    """Main demo function"""
    print("🎯 JIRA RQUID Extractor - Connection Demo")
    print("="*50)
    
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"JIRA Domain: {JIRA_DOMAIN}")
    print(f"Email: {EMAIL}")
    
    # Test JIRA connection and show sample data
    if test_jira_connection():
        show_setup_instructions()
    else:
        print("\n❌ Please fix JIRA connection issues before proceeding.")
        print("Check your JIRA_API_TOKEN in the .env file.")

if __name__ == "__main__":
    main()
