{"took": 2142, "timed_out": false, "_shards": {"total": 78, "successful": 78, "skipped": 0, "failed": 0}, "hits": {"total": {"value": 2, "relation": "eq"}, "max_score": 17.702923, "hits": [{"_index": ".ds-gw-prd-2025.06.20-002914", "_id": "L7KnjZcBDTQjCI2Q1LIb", "_score": 17.702923, "_source": {"jsonResp": {"requestUrl": "https://intapigw.scb.co.th:8448/scb/rest/ent-api/v1/cust-profile/custinfo/**************/creditCards?", "serviceName": "ent_MS_CustProfile_CustInfo", "routingCode": "200", "RequestUID": "78148764314453026234", "Latency": 573, "routingUrl": "https://hybrid-eapi.scb.co.th:8443/scb/rest/ent-api/v1/cust-profile/custinfo/**************/creditCards"}, "@timestamp": "2025-06-20T14:04:16.611Z"}}, {"_index": ".ds-gw-prd-2025.06.20-002914", "_id": "Ep2njZcB6woTdkmY0LTF", "_score": 17.702904, "_source": {"jsonResp": {"RequestUID": "78148764314453026234", "serviceName": "ent_External_MS_Proxy_Inbound", "requestUrl": "https://b2bapi.scb:8448/scb/rest/b2b-api/v1/support/utility/proxy/inbound", "Latency": "595", "routingCode": "200", "routingUrl": "https://intapigw.scb.co.th:8448/scb/rest/ent-api/v1/cust-profile/custinfo/**************/creditCards"}, "@timestamp": "2025-06-20T14:04:16.616Z"}}]}}