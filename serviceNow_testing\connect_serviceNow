import requests
from requests.auth import HTTP<PERSON>asicAuth

# Replace with your ServiceNow credentials
username = '<EMAIL>'
password = 'Karaked1234@'

# ServiceNow instance and endpoint
instance = 'scb.service-now.com'
url = f'https://{instance}/api/now/table/incident_task'

# Example query: active=true, assignment_group=..., state=1
query = (
    'active=true^assignment_groupDYNAMICd6435e965f510100a9ad2572f2b47744^state=1'
)
params = {
    'sysparm_query': query,
    'sysparm_limit': 5  # Limit results for demo
}

response = requests.get(
    url,
    auth=HTTPBasicAuth(username, password),
    params=params,
    headers={'Accept': 'application/json'}
)

if response.status_code == 200:
    data = response.json()
    for task in data['result']:
        print(f"Number: {task['number']}, Short Description: {task['short_description']}")
else:
    print(f"Failed to fetch data: {response.status_code} {response.text}")