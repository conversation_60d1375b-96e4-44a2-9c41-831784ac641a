{"nodes": [{"parameters": {"requestMethod": "POST", "url": "https://eapi.kb.ceceop.scb.co.th:9243/api/console/proxy", "queryParametersUi": {"parameter": [{"name": "path", "value": "gw-prd/_search"}, {"name": "method", "value": "POST"}]}, "headersUi": {"parameter": [{"name": "Content-Type", "value": "application/json"}, {"name": "kbn-xsrf", "value": "true"}, {"name": "Authorization", "value": "Basic ZWNlLWVhcGktYWQtdXNlcjp0RiFlU3EtdWdhUEpAMw=="}]}, "options": {}, "bodyParametersJson": "{\n  \"from\": 0,\n  \"size\": 100,\n  \"_source\": [\"jsonResp.Latency\"],\n  \"query\": {\n    \"bool\": {\n      \"must\": [\n        { \"match\": { \"jsonResp.RequestUID\": \"={{$json[\"request_uid\"] || \"YOUR_REQUEST_UID_HERE\"}}\" } },\n        { \"match\": { \"jsonResp.Level\": \"AUDIT\" } }\n      ]\n    }\n  }\n}"}, "id": "HTTP_Request", "name": "Kibana Query", "type": "n8n-nodes-base.httpRequest", "typeVersion": 2, "position": [400, 300]}, {"parameters": {"functionCode": "let total = 0;\nlet valid = 0;\nconst hits = $json.hits?.hits || [];\nfor (const hit of hits) {\n  let latency = hit._source?.jsonResp?.Latency || hit._source?.jsonResp?.latency;\n  if (latency !== undefined) {\n    total += parseFloat(latency);\n    valid++;\n  }\n}\nreturn [{ json: { total_latency: valid > 0 ? total : 'Error' } }];"}, "id": "Sum_Latency", "name": "Sum Total Latency", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [600, 300]}, {"parameters": {"fields": [{"name": "request_uid", "value": "YOUR_REQUEST_UID_HERE"}], "options": {}}, "id": "Set_RQUID", "name": "Set RQUID", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [200, 300]}], "connections": {"Set RQUID": {"main": [[{"node": "Kibana Query", "type": "main", "index": 0}]]}, "Kibana Query": {"main": [[{"node": "Sum Total Latency", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "name": "Kibana Query Total Latency", "versionId": "1"}