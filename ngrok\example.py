#!/usr/bin/env python

from http.server import HTTPServer, BaseHTTPRequestHandler
import logging
from pyngrok import ngrok


class HelloHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        body = bytes("Hello", "utf-8")
        self.protocol_version = "HTTP/1.1"
        self.send_response(200)
        self.send_header("Content-Length", len(body))
        self.end_headers()
        self.wfile.write(body)

logging.basicConfig(level=logging.INFO)

# Create the server
server = HTTPServer(("localhost", 0), HelloHandler)
port = server.server_address[1]

# Start ngrok tunnel
public_tunnel = ngrok.connect(port)
print(f"ngrok tunnel \"{public_tunnel.public_url}\" -> \"http://127.0.0.1:{port}\"")
logging.info(f"Public URL: {public_tunnel.public_url}")

try:
    logging.info("Starting server. Press Ctrl+C to stop.")
    server.serve_forever()
except KeyboardInterrupt:
    logging.info("Shutting down server...")
    server.server_close()
    ngrok.disconnect(public_tunnel.public_url)
    ngrok.kill()
    logging.info("Server stopped cleanly.")