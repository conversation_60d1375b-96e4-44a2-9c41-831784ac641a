import requests
import sys
import urllib3

# Suppress SSL warnings for internal corporate endpoints
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def query_kibana_simple(request_uid):
    url = "https://eapi.kb.ceceop.scb.co.th:9243/api/console/proxy"
    params = {
        'path': 'gw-prd/_search',
        'method': 'POST'
    }
    headers = {
        'Content-Type': 'application/json',
        'kbn-xsrf': 'true',
        'Authorization': 'Basic ZWNlLWVhcGktYWQtdXNlcjp0RiFlU3EtdWdhUEpAMw=='
    }
    payload = {
        "from": 0,
        "size": 100,
        "_source": ["jsonResp.Latency"],
        "query": {
            "bool": {
                "must": [
                    {"match": {"jsonResp.RequestUID": request_uid}},
                    {"match": {"jsonResp.Level": "AUDIT"}}
                ]
            }
        }
    }
    try:
        response = requests.post(url, params=params, headers=headers, json=payload, verify=False)
        response.raise_for_status()
        data = response.json()
        hits = data.get('hits', {}).get('hits', [])
        total_latency = 0
        valid_records = 0
        for hit in hits:
            source = hit.get('_source', {})
            latency = (
                source.get('jsonResp', {}).get('Latency') or
                source.get('jsonResp', {}).get('latency') or
                source.get('Latency') or
                source.get('latency')
            )
            if latency is not None:
                try:
                    latency_num = float(str(latency).replace('ms', ''))
                    total_latency += latency_num
                    valid_records += 1
                except Exception:
                    continue
        if valid_records > 0:
            print(int(total_latency))
        else:
            print('Error')
    except Exception:
        print('Error')

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Error")
    else:
        query_kibana_simple(sys.argv[1])
