#!/usr/bin/env python3
"""
JIRA-Kibana Integration Script

This script:
1. Extracts RQUIDs from JIRA tickets using OpenTyphoon.ai
2. Queries Kibana for additional context using the extracted RQUIDs
3. Correlates JIRA tickets with Kibana logs for comprehensive analysis

Requirements:
- JIRA_API_TOKEN environment variable
- OPENTYPHOON_API_KEY environment variable
- Network access to Kibana endpoint

Author: AI Assistant
Date: 2025-06-26
"""

import os
import json
import requests
import base64
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import logging

# Third-party imports
from jira import JIRA
from openai import OpenAI
from dotenv import load_dotenv
import pandas as pd

# Load environment variables
load_dotenv()

# Configuration
JIRA_DOMAIN = "https://scbjira.atlassian.net"
EMAIL = "<EMAIL>"
JIRA_API_TOKEN = os.getenv('JIRA_API_TOKEN')
OPENTYPHOON_API_KEY = os.getenv('OPENTYPHOON_API_KEY')

# Kibana configuration
KIBANA_URL = "https://eapi.kb.ceceop.scb.co.th:9243/api/console/proxy"
KIBANA_USERNAME = "ece-eapi-ad-user"
KIBANA_PASSWORD = "tF!eSq-ugaPJ@3"  # From the base64 decoded auth header
KIBANA_AUTH = base64.b64encode(f"{KIBANA_USERNAME}:{KIBANA_PASSWORD}".encode()).decode()

# OpenTyphoon.ai configuration
OPENTYPHOON_BASE_URL = "https://api.opentyphoon.ai/v1"
OPENTYPHOON_MODEL = "typhoon-v2-70b-instruct"

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('jira_kibana_integration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class JiraKibanaIntegrator:
    """Integrates JIRA ticket analysis with Kibana log queries"""
    
    def __init__(self):
        """Initialize the integrator"""
        self.jira_client = None
        self.typhoon_client = None
        self.results = []
        
        # Validate environment variables
        if not JIRA_API_TOKEN:
            raise ValueError("JIRA_API_TOKEN environment variable not set!")
        if not OPENTYPHOON_API_KEY:
            raise ValueError("OPENTYPHOON_API_KEY environment variable not set!")
        
        self._initialize_clients()
    
    def _initialize_clients(self):
        """Initialize JIRA and OpenTyphoon.ai clients"""
        try:
            # Initialize JIRA client
            self.jira_client = JIRA(
                server=JIRA_DOMAIN,
                basic_auth=(EMAIL, JIRA_API_TOKEN)
            )
            logger.info("✅ Successfully connected to JIRA!")
            
            # Initialize OpenTyphoon.ai client
            self.typhoon_client = OpenAI(
                api_key=OPENTYPHOON_API_KEY,
                base_url=OPENTYPHOON_BASE_URL
            )
            logger.info("✅ Successfully initialized OpenTyphoon.ai client!")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize clients: {e}")
            raise
    
    def extract_rquid_from_text(self, text: str, ticket_id: str) -> Tuple[Optional[str], float, str]:
        """Extract RQUID from text using OpenTyphoon.ai"""
        if not text or not text.strip():
            return None, 0.0, "Empty text"
        
        system_prompt = """คุณเป็นผู้เชี่ยวชาญในการสกัดข้อมูล Request UID (RQUID) จากข้อความภาษาไทย

งานของคุณคือ:
1. ค้นหา Request UID ในรูปแบบต่างๆ เช่น:
   - requestUID = [ตัวเลข/ตัวอักษร]
   - RQUID: [ตัวเลข/ตัวอักษร]  
   - Request UID: [ตัวเลข/ตัวอักษร]
   - requestId = [ตัวเลข/ตัวอักษร]

2. ตอบกลับในรูปแบบ JSON เท่านั้น:
   {
     "rquid": "Request UID ที่พบ หรือ null ถ้าไม่พบ",
     "confidence": ตัวเลขระหว่าง 0.0-1.0,
     "found_pattern": "รูปแบบที่พบ หรือ null"
   }"""
        
        user_prompt = f"กรุณาสกัด Request UID จากข้อความนี้:\n\n{text}"
        
        try:
            response = self.typhoon_client.chat.completions.create(
                model=OPENTYPHOON_MODEL,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1,
                max_tokens=200
            )
            
            result_text = response.choices[0].message.content.strip()
            result = json.loads(result_text)
            
            rquid = result.get('rquid')
            confidence = float(result.get('confidence', 0.0))
            status = f"Success - Pattern: {result.get('found_pattern', 'N/A')}"
            
            return rquid, confidence, status
            
        except Exception as e:
            logger.error(f"Failed to extract RQUID for {ticket_id}: {e}")
            return None, 0.0, f"Error: {str(e)}"
    
    def query_kibana(self, rquid: str) -> Dict:
        """Query Kibana for logs related to the RQUID"""
        
        kibana_query = {
            "from": 0,
            "size": 100,
            "_source": [
                "jsonResp.RequestUID",
                "jsonResp.requestUrl",
                "jsonResp.routingUrl", 
                "jsonResp.routingCode",
                "jsonResp.serviceName",
                "jsonResp.Latency",
                "@timestamp"
            ],
            "query": {
                "bool": {
                    "must": [
                        {
                            "match": {
                                "jsonResp.RequestUID": rquid
                            }
                        },
                        {
                            "match": {
                                "jsonResp.Level": "AUDIT"
                            }
                        }
                    ]
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'kbn-xsrf': 'true',
            'Authorization': f'Basic {KIBANA_AUTH}'
        }
        
        try:
            logger.info(f"🔍 Querying Kibana for RQUID: {rquid}")
            
            response = requests.post(
                f"{KIBANA_URL}?path=gw-prd%2F_search&method=POST",
                headers=headers,
                json=kibana_query,
                timeout=30,
                verify=False  # Note: In production, use proper SSL verification
            )
            
            if response.status_code == 200:
                kibana_data = response.json()
                hits = kibana_data.get('hits', {}).get('hits', [])
                
                logger.info(f"✅ Kibana query successful - Found {len(hits)} records for RQUID: {rquid}")
                
                return {
                    'success': True,
                    'total_hits': len(hits),
                    'records': hits,
                    'query_time': datetime.now().isoformat()
                }
            else:
                logger.error(f"❌ Kibana query failed - Status: {response.status_code}, Response: {response.text}")
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}: {response.text}",
                    'query_time': datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"❌ Kibana query exception for RQUID {rquid}: {e}")
            return {
                'success': False,
                'error': str(e),
                'query_time': datetime.now().isoformat()
            }
    
    def process_jira_ticket_with_kibana(self, ticket_id: str) -> Dict:
        """Process a single JIRA ticket and query Kibana for related logs"""
        
        try:
            # Get JIRA ticket
            issue = self.jira_client.issue(ticket_id, expand='description')
            summary = issue.fields.summary or ""
            description = getattr(issue.fields, 'description', '') or ""
            full_text = f"{summary}\n\n{description}".strip()
            
            logger.info(f"🎫 Processing ticket: {ticket_id}")
            
            # Extract RQUID
            rquid, confidence, extraction_status = self.extract_rquid_from_text(full_text, ticket_id)
            
            result = {
                'ticket_id': ticket_id,
                'summary': summary,
                'rquid': rquid,
                'extraction_confidence': confidence,
                'extraction_status': extraction_status,
                'kibana_data': None,
                'processed_at': datetime.now().isoformat()
            }
            
            # If RQUID found, query Kibana
            if rquid:
                logger.info(f"✅ Found RQUID: {rquid} - Querying Kibana...")
                kibana_result = self.query_kibana(rquid)
                result['kibana_data'] = kibana_result
                
                if kibana_result['success']:
                    logger.info(f"🎯 Correlation successful! Found {kibana_result['total_hits']} Kibana records for {ticket_id}")
                else:
                    logger.warning(f"⚠️ Kibana query failed for {ticket_id}: {kibana_result.get('error', 'Unknown error')}")
            else:
                logger.info(f"❌ No RQUID found for {ticket_id} - Skipping Kibana query")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Error processing ticket {ticket_id}: {e}")
            return {
                'ticket_id': ticket_id,
                'summary': 'Error retrieving ticket',
                'rquid': None,
                'extraction_confidence': 0.0,
                'extraction_status': f"Error: {str(e)}",
                'kibana_data': None,
                'processed_at': datetime.now().isoformat()
            }


def main():
    """Main function to demonstrate JIRA-Kibana integration"""
    try:
        integrator = JiraKibanaIntegrator()
        
        # Test with a known ticket that has RQUID
        test_ticket = "AIP-5642"  # This ticket had RQUID: 78148764314453026234
        
        print("🚀 JIRA-Kibana Integration Demo")
        print("="*50)
        print(f"Testing with ticket: {test_ticket}")
        
        # Process the ticket
        result = integrator.process_jira_ticket_with_kibana(test_ticket)
        
        # Display results
        print(f"\n📋 JIRA Ticket Analysis:")
        print(f"   Ticket ID: {result['ticket_id']}")
        print(f"   Summary: {result['summary'][:100]}...")
        print(f"   RQUID: {result['rquid']}")
        print(f"   Confidence: {result['extraction_confidence']}")
        
        if result['kibana_data']:
            kibana_data = result['kibana_data']
            if kibana_data['success']:
                print(f"\n🔍 Kibana Query Results:")
                print(f"   Total records found: {kibana_data['total_hits']}")
                
                if kibana_data['total_hits'] > 0:
                    print(f"   Sample record details:")
                    for i, record in enumerate(kibana_data['records'][:3]):  # Show first 3 records
                        source = record.get('_source', {})
                        json_resp = source.get('jsonResp', {})
                        print(f"     Record {i+1}:")
                        print(f"       Service: {json_resp.get('serviceName', 'N/A')}")
                        print(f"       URL: {json_resp.get('requestUrl', 'N/A')}")
                        print(f"       Latency: {json_resp.get('Latency', 'N/A')}")
                        print(f"       Timestamp: {source.get('@timestamp', 'N/A')}")
            else:
                print(f"\n❌ Kibana Query Failed:")
                print(f"   Error: {kibana_data.get('error', 'Unknown error')}")
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"jira_kibana_integration_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Detailed results saved to: {filename}")
        
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        raise


if __name__ == "__main__":
    main()
