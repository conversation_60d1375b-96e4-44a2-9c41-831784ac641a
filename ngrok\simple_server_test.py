#!/usr/bin/env python

from http.server import HTTPServer, BaseHTTPRequestHandler
import logging
import socket
import threading
import time

class HelloHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        body = f"""
        <html>
        <head><title>Test Server</title></head>
        <body>
            <h1>Hello from Python Server!</h1>
            <p>Server is running on: {self.server.server_address}</p>
            <p>Time: {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>Path: {self.path}</p>
        </body>
        </html>
        """.encode('utf-8')
        
        self.protocol_version = "HTTP/1.1"
        self.send_response(200)
        self.send_header("Content-Type", "text/html")
        self.send_header("Content-Length", len(body))
        self.end_headers()
        self.wfile.write(body)
        
    def log_message(self, format, *args):
        logging.info(f"{self.address_string()} - {format % args}")

def get_local_ip():
    """Get the local IP address"""
    try:
        # Connect to a remote address to determine local IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "127.0.0.1"

def main():
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Create the server
    server = HTTPServer(("0.0.0.0", 0), HelloHandler)  # 0.0.0.0 to accept connections from any IP
    port = server.server_address[1]
    local_ip = get_local_ip()
    
    print("=" * 60)
    print("🚀 Python HTTP Server Started!")
    print("=" * 60)
    print(f"Local URL:    http://localhost:{port}")
    print(f"Network URL:  http://{local_ip}:{port}")
    print(f"Server bound to: {server.server_address}")
    print("=" * 60)
    print("📝 Note: Since ngrok is blocked by your organization's security policy,")
    print("   this server is only accessible within your local network.")
    print("   To make it publicly accessible, you would need:")
    print("   1. IT approval to use ngrok or similar tunneling services")
    print("   2. Alternative solutions like:")
    print("      - localtunnel (npm install -g localtunnel)")
    print("      - serveo.net (ssh -R 80:localhost:port serveo.net)")
    print("      - Your organization's approved reverse proxy/tunneling solution")
    print("=" * 60)
    print("Press Ctrl+C to stop the server")
    print("=" * 60)
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        logging.info("Shutting down server...")
        server.server_close()
        print("\n🛑 Server stopped cleanly.")

if __name__ == "__main__":
    main()
