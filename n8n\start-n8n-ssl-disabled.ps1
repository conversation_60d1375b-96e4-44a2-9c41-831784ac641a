#!/usr/bin/env powershell

Write-Host "Starting n8n with SSL certificate verification disabled..." -ForegroundColor Yellow
Write-Host "This is for development/corporate network environments only." -ForegroundColor Yellow
Write-Host ""

# Set Node.js environment variable to ignore SSL certificate errors
$env:NODE_TLS_REJECT_UNAUTHORIZED = "0"

# Display warning
Write-Host "WARNING: SSL certificate verification is disabled!" -ForegroundColor Red
Write-Host "Only use this in trusted corporate networks." -ForegroundColor Red
Write-Host ""

# Start n8n
Write-Host "Starting n8n..." -ForegroundColor Green
npx n8n

Write-Host ""
Write-Host "n8n has been stopped." -ForegroundColor Yellow
