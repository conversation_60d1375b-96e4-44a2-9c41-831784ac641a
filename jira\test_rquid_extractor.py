#!/usr/bin/env python3
"""
Test script for JIRA RQUID Extractor

This script tests the RQUID extraction functionality with sample data
before running on actual JIRA tickets.

Usage:
1. Set up your API keys in .env file
2. Run: python test_rquid_extractor.py
"""

import os
import json
from dotenv import load_dotenv
from openai import OpenAI

# Load environment variables
load_dotenv()

# Test data with various RQUID formats
TEST_CASES = [
    {
        "ticket_id": "TEST-001",
        "text": "ระบบชำระเงินมีปัญหา requestUID = 78148764314453026234 ไม่สามารถดำเนินการได้",
        "expected_rquid": "78148764314453026234"
    },
    {
        "ticket_id": "TEST-002", 
        "text": "รายงานแสดงไม่ถูกต้อง RQUID: 12345678901234567890 กรุณาตรวจสอบ",
        "expected_rquid": "12345678901234567890"
    },
    {
        "ticket_id": "TEST-003",
        "text": "ไม่ได้รับสลิป Request UID: 99887766554433221100 ระบบมีข้อผิดพลาด",
        "expected_rquid": "99887766554433221100"
    },
    {
        "ticket_id": "TEST-004",
        "text": "ปัญหาการแจ้งเตือน requestId = 11223344556677889900 ไม่ทำงาน",
        "expected_rquid": "11223344556677889900"
    },
    {
        "ticket_id": "TEST-005",
        "text": "ระบบล่ม ไม่มีข้อมูล Request UID ในข้อความนี้",
        "expected_rquid": None
    }
]

def test_opentyphoon_connection():
    """Test connection to OpenTyphoon.ai API"""
    api_key = os.getenv('OPENTYPHOON_API_KEY')
    
    if not api_key or api_key == 'your_opentyphoon_api_key_here':
        print("❌ OpenTyphoon.ai API key not set!")
        print("Please:")
        print("1. Sign up at https://opentyphoon.ai")
        print("2. Create an API key")
        print("3. Update OPENTYPHOON_API_KEY in .env file")
        return False
    
    try:
        client = OpenAI(
            api_key=api_key,
            base_url="https://api.opentyphoon.ai/v1"
        )
        
        # Test with a simple request
        response = client.chat.completions.create(
            model="typhoon-v2-70b-instruct",
            messages=[
                {"role": "user", "content": "สวัสดี"}
            ],
            max_tokens=10
        )
        
        print("✅ OpenTyphoon.ai connection successful!")
        print(f"Test response: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ OpenTyphoon.ai connection failed: {e}")
        return False

def test_rquid_extraction():
    """Test RQUID extraction with sample data"""
    api_key = os.getenv('OPENTYPHOON_API_KEY')
    
    if not api_key or api_key == 'your_opentyphoon_api_key_here':
        print("❌ Cannot test extraction without valid API key")
        return
    
    client = OpenAI(
        api_key=api_key,
        base_url="https://api.opentyphoon.ai/v1"
    )
    
    system_prompt = """คุณเป็นผู้เชี่ยวชาญในการสกัดข้อมูล Request UID (RQUID) จากข้อความภาษาไทย

งานของคุณคือ:
1. ค้นหา Request UID ในรูปแบบต่างๆ เช่น:
   - requestUID = [ตัวเลข]
   - RQUID: [ตัวเลข]  
   - Request UID: [ตัวเลข]
   - requestId = [ตัวเลข]
   - หรือรูปแบบอื่นที่คล้ายกัน

2. ตอบกลับในรูปแบบ JSON เท่านั้น:
   {
     "rquid": "ตัวเลข Request UID ที่พบ หรือ null ถ้าไม่พบ",
     "confidence": ตัวเลขระหว่าง 0.0-1.0 แสดงความมั่นใจ,
     "found_pattern": "รูปแบบที่พบ เช่น 'requestUID = 123' หรือ null"
   }

3. ถ้าไม่พบ Request UID ให้ตอบ:
   {
     "rquid": null,
     "confidence": 0.0,
     "found_pattern": null
   }"""
    
    print("\n" + "="*60)
    print("TESTING RQUID EXTRACTION")
    print("="*60)
    
    correct_extractions = 0
    total_tests = len(TEST_CASES)
    
    for test_case in TEST_CASES:
        print(f"\n🧪 Testing {test_case['ticket_id']}:")
        print(f"Text: {test_case['text']}")
        print(f"Expected RQUID: {test_case['expected_rquid']}")
        
        try:
            response = client.chat.completions.create(
                model="typhoon-v2-70b-instruct",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"กรุณาสกัด Request UID จากข้อความนี้:\n\n{test_case['text']}"}
                ],
                temperature=0.1,
                max_tokens=200
            )
            
            result_text = response.choices[0].message.content.strip()
            print(f"Raw response: {result_text}")
            
            try:
                result = json.loads(result_text)
                extracted_rquid = result.get('rquid')
                confidence = result.get('confidence', 0.0)
                pattern = result.get('found_pattern')
                
                print(f"Extracted RQUID: {extracted_rquid}")
                print(f"Confidence: {confidence}")
                print(f"Pattern: {pattern}")
                
                # Check if extraction is correct
                if extracted_rquid == test_case['expected_rquid']:
                    print("✅ CORRECT!")
                    correct_extractions += 1
                else:
                    print("❌ INCORRECT!")
                    
            except json.JSONDecodeError:
                print("❌ Failed to parse JSON response")
                
        except Exception as e:
            print(f"❌ API Error: {e}")
    
    print(f"\n📊 Test Results: {correct_extractions}/{total_tests} correct ({correct_extractions/total_tests*100:.1f}%)")

def main():
    """Main test function"""
    print("🧪 JIRA RQUID Extractor - Test Suite")
    print("="*50)
    
    # Test 1: Check environment setup
    print("\n1. Testing environment setup...")
    jira_token = os.getenv('JIRA_API_TOKEN')
    typhoon_key = os.getenv('OPENTYPHOON_API_KEY')
    
    if not jira_token:
        print("❌ JIRA_API_TOKEN not set")
    else:
        print("✅ JIRA_API_TOKEN is set")
    
    if not typhoon_key or typhoon_key == 'your_opentyphoon_api_key_here':
        print("❌ OPENTYPHOON_API_KEY not set properly")
    else:
        print("✅ OPENTYPHOON_API_KEY is set")
    
    # Test 2: Test OpenTyphoon.ai connection
    print("\n2. Testing OpenTyphoon.ai connection...")
    if test_opentyphoon_connection():
        # Test 3: Test RQUID extraction
        print("\n3. Testing RQUID extraction...")
        test_rquid_extraction()
    else:
        print("⏭️ Skipping extraction tests due to connection failure")
    
    print("\n🎉 Test suite completed!")
    print("\nNext steps:")
    print("1. If tests pass, run: python jira_rquid_extractor.py")
    print("2. Check the generated CSV file for results")
    print("3. Review the log file for detailed processing information")

if __name__ == "__main__":
    main()
