{"ticket_id": "AIP-5642", "summary": "TASKSC250626-0189403 [Inquiry-Advice-Help/Application/Account Service] - Account Info", "rquid": "78148764314453026234", "extraction_confidence": 1.0, "extraction_status": "Success - Pattern: requestUID = [ตัวเลข]", "kibana_data": {"success": false, "error": "HTTPSConnectionPool(host='eapi.kb.ceceop.scb.co.th', port=9243): Max retries exceeded with url: /api/console/proxy?path=gw-prd%2F_search&method=POST (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000002750A15F090>: Failed to resolve 'eapi.kb.ceceop.scb.co.th' ([Errno 11001] getaddrinfo failed)\"))", "query_time": "2025-06-26T20:18:39.703716"}, "processed_at": "2025-06-26T20:18:39.631929"}