# JIRA Request UID Extractor

A Python script that processes JIRA tickets from the "API Implementation - Production" (AIP) project to extract Request UID (RQUID) values from Thai language text using OpenTyphoon.ai API.

## Features

- 🎯 **Intelligent Extraction**: Uses OpenTyphoon.ai's Thai language model to identify Request UIDs in various formats
- 🔄 **Batch Processing**: Processes tickets in batches with rate limiting to avoid API limits
- 📊 **Comprehensive Reporting**: Generates detailed CSV reports with extraction confidence scores
- 🛡️ **Error Handling**: Robust error handling with retry mechanisms and fallback extraction
- 📝 **Detailed Logging**: Complete logging of processing steps and results
- 🧪 **Testing Suite**: Includes test script to verify setup and functionality

## Supported RQUID Formats

The script can extract Request UIDs in various formats commonly found in Thai text:

- `requestUID = [number]`
- `RQUID: [number]`
- `Request UID: [number]`
- `requestId = [number]`
- And other similar patterns embedded in error messages or technical details

## Prerequisites

1. **Python 3.11+** (Windows Store Python or system Python)
2. **JIRA Access**: Valid JIRA API token for scbjira.atlassian.net
3. **OpenTyphoon.ai API Key**: Sign up at [OpenTyphoon.ai](https://opentyphoon.ai)

## Installation

1. **Clone or download the project files**:
   ```
   jira_rquid_extractor.py
   test_rquid_extractor.py
   .env
   requirements.txt (if created)
   ```

2. **Install required Python packages**:
   ```bash
   pip install jira openai python-dotenv pandas tqdm
   ```

3. **Set up environment variables**:
   
   Edit the `.env` file and add your API keys:
   ```env
   # JIRA Configuration
   JIRA_API_TOKEN=your_jira_api_token_here
   
   # OpenTyphoon.ai Configuration
   OPENTYPHOON_API_KEY=your_opentyphoon_api_key_here
   ```

## Getting API Keys

### JIRA API Token
1. Go to [Atlassian Account Settings](https://id.atlassian.com/manage-profile/security/api-tokens)
2. Click "Create API token"
3. Give it a label (e.g., "RQUID Extractor")
4. Copy the generated token

### OpenTyphoon.ai API Key
1. Visit [OpenTyphoon.ai](https://opentyphoon.ai)
2. Sign up for an account
3. Navigate to API Keys section in your dashboard
4. Click "Create new API key"
5. Copy the generated key

## Usage

### 1. Test Setup (Recommended)

First, run the test script to verify your setup:

```bash
python test_rquid_extractor.py
```

This will:
- Check if environment variables are set correctly
- Test connection to OpenTyphoon.ai API
- Run sample RQUID extractions to verify functionality

### 2. Run Main Extraction

Once tests pass, run the main extraction:

```bash
python jira_rquid_extractor.py
```

The script will:
- Connect to JIRA and fetch all open AIP tickets
- Process each ticket to extract Request UIDs
- Save results to a timestamped CSV file
- Generate a detailed log file

### 3. Review Results

Check the generated files:
- **CSV Report**: `jira_rquid_extraction_results_YYYYMMDD_HHMMSS.csv`
- **Log File**: `jira_rquid_extraction.log`

## Output Format

### CSV Columns
- `Ticket_ID`: JIRA ticket identifier (e.g., AIP-5645)
- `Summary`: Ticket summary/title
- `Request_UID`: Extracted Request UID value (or null if not found)
- `Extraction_Confidence`: Confidence score (0.0-1.0) from OpenTyphoon.ai
- `Processing_Status`: Status of extraction process
- `Full_Text_Length`: Length of analyzed text
- `Processed_At`: Timestamp of processing

### Console Output Example
```
Ticket ID: AIP-5645
Summary: TASKSC250626-0189544 [Inquiry-Advice-Help/Application/Payment] - ชำระ ตัดเงินต้นทาง ปลายทางไม่ได้รับ
Request UID: 78148764314453026234
Confidence: 0.95
Status: ✅ Successfully extracted
```

## Configuration

### Processing Settings
You can modify these settings in `jira_rquid_extractor.py`:

```python
BATCH_SIZE = 5  # Process tickets in batches
DELAY_BETWEEN_BATCHES = 2  # Seconds between batches
MAX_RETRIES = 3  # Maximum API retries
```

### OpenTyphoon.ai Model
The script uses `typhoon-v2-70b-instruct` model. You can change this in the configuration section if needed.

## Error Handling

The script handles various error scenarios:

- **API Connection Failures**: Automatic retries with exponential backoff
- **Rate Limiting**: Batch processing with delays
- **Invalid Responses**: Fallback regex extraction
- **Missing Data**: Graceful handling of empty ticket descriptions
- **Network Issues**: Comprehensive error logging

## Troubleshooting

### Common Issues

1. **"ModuleNotFoundError: No module named 'jira'"**
   - Install packages for the correct Python version
   - Use the same Python executable for installation and running

2. **"JIRA_API_TOKEN environment variable not set"**
   - Check your `.env` file
   - Ensure the file is in the same directory as the script

3. **"Invalid API key provided" (OpenTyphoon.ai)**
   - Verify your API key is correct
   - Check if you have sufficient API credits

4. **"Failed to connect to JIRA"**
   - Verify your JIRA token is valid
   - Check network connectivity

### Debug Mode

For detailed debugging, modify the logging level:

```python
logging.basicConfig(level=logging.DEBUG)
```

## Rate Limits

- **JIRA API**: Generally allows 300 requests per minute
- **OpenTyphoon.ai**: Check your plan limits
- The script includes built-in rate limiting to stay within bounds

## Security Notes

- Never commit API keys to version control
- The `.env` file is included in `.gitignore`
- Use environment variables in production
- Rotate API keys periodically

## Support

For issues or questions:
1. Check the log file for detailed error messages
2. Run the test script to isolate problems
3. Verify API key validity and permissions
4. Contact OpenTyphoon.ai support for API-related issues

## License

This script is provided as-is for internal use. Ensure compliance with your organization's policies and the terms of service for JIRA and OpenTyphoon.ai APIs.
