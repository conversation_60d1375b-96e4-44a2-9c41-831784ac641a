#!/usr/bin/env powershell

Write-Host "Starting n8n with Corporate SSL Certificates..." -ForegroundColor Green
Write-Host "This uses your organization's certificates for secure HTTPS requests." -ForegroundColor Green
Write-Host ""

# Get the current directory
$currentDir = Get-Location

# Set Node.js to use corporate certificates
$certBundle = Join-Path $currentDir "corporate-ca-bundle.pem"
$env:NODE_EXTRA_CA_CERTS = $certBundle

# Display certificate info
Write-Host "Using certificate bundle: $certBundle" -ForegroundColor Yellow
Write-Host ""

# Verify certificate file exists
if (-not (Test-Path $certBundle)) {
    Write-Host "ERROR: Certificate bundle not found!" -ForegroundColor Red
    Write-Host "Please ensure corporate-ca-bundle.pem exists in the current directory." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Certificate bundle found. Starting n8n..." -ForegroundColor Green
Write-Host ""

# Display certificate details
Write-Host "Certificate Bundle Contents:" -ForegroundColor Cyan
$certContent = Get-Content $certBundle | Select-String "BEGIN CERTIFICATE" -Context 0,5
$certCount = ($certContent | Measure-Object).Count
Write-Host "Found $certCount certificate(s) in bundle" -ForegroundColor Cyan
Write-Host ""

# Start n8n with corporate certificates
Write-Host "Launching n8n with secure SSL configuration..." -ForegroundColor Green
npx n8n

Write-Host ""
Write-Host "n8n has been stopped." -ForegroundColor Yellow
