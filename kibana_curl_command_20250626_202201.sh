#!/bin/bash
# Kibana query for RQUID: 78148764314453026234
# Generated: 2025-06-26T20:22:01.669479

curl --location --request POST 'https://eapi.kb.ceceop.scb.co.th:9243/api/console/proxy?path=gw-prd%2F_search&method=POST' \
--header 'Content-Type: application/json' \
--header 'kbn-xsrf: true' \
--header 'Authorization: Basic ZWNlLWVhcGktYWQtdXNlcjp0RiFlU3EtdWdhUEpAMw==' \
--data-raw '{"from":0,"size":100,"_source":["jsonResp.RequestUID","jsonResp.requestUrl","jsonResp.routingUrl","jsonResp.routingCode","jsonResp.serviceName","jsonResp.Latency","@timestamp"],"query":{"bool":{"must":[{"match":{"jsonResp.RequestUID":"78148764314453026234"}},{"match":{"jsonResp.Level":"AUDIT"}}]}}}'