#!/usr/bin/env python

from http.server import HTTPServer, BaseHTTPRequestHandler
import logging
import webbrowser
import threading
import time

class HelloHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        body = f"""
        <html>
        <head><title>ngrok Test Server</title></head>
        <body style="font-family: Arial, sans-serif; margin: 40px;">
            <h1>🎉 Server is Working!</h1>
            <p><strong>Time:</strong> {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p><strong>Path:</strong> {self.path}</p>
            <p><strong>Server Address:</strong> {self.server.server_address}</p>
            <hr>
            <h2>ngrok Status</h2>
            <p>❌ ngrok binary download is blocked by your organization's security policy.</p>
            <p>✅ pyngrok Python library is installed successfully.</p>
            <p>✅ Basic HTTP server functionality is working.</p>
            <hr>
            <h3>Next Steps:</h3>
            <ul>
                <li>Contact IT to whitelist ngrok downloads from bin.equinox.io</li>
                <li>Or use alternative tunneling solutions</li>
                <li>Or manually install ngrok binary if permitted</li>
            </ul>
        </body>
        </html>
        """.encode('utf-8')
        
        self.protocol_version = "HTTP/1.1"
        self.send_response(200)
        self.send_header("Content-Type", "text/html")
        self.send_header("Content-Length", len(body))
        self.end_headers()
        self.wfile.write(body)
        
    def log_message(self, format, *args):
        print(f"📝 {self.address_string()} - {format % args}")

def open_browser_after_delay(url, delay=2):
    """Open browser after a short delay"""
    time.sleep(delay)
    webbrowser.open(url)

def main():
    # Create the server on localhost
    server = HTTPServer(("localhost", 0), HelloHandler)
    port = server.server_address[1]
    url = f"http://localhost:{port}"
    
    print("=" * 60)
    print("🚀 ngrok Test Server Started!")
    print("=" * 60)
    print(f"URL: {url}")
    print("=" * 60)
    print("Opening browser in 2 seconds...")
    print("Press Ctrl+C to stop the server")
    print("=" * 60)
    
    # Open browser in a separate thread
    browser_thread = threading.Thread(target=open_browser_after_delay, args=(url,))
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped.")
        server.server_close()

if __name__ == "__main__":
    main()
