import os
from jira import JIRA
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Replace with your JIRA credentials and domain
JIRA_DOMAIN = "https://scbjira.atlassian.net"
EMAIL = "<EMAIL>"
# Use environment variable for security
API_TOKEN = os.getenv('JIRA_API_TOKEN')

if not API_TOKEN:
    print("❌ Error: JIRA_API_TOKEN environment variable not set!")
    print("Please set your API token as an environment variable:")
    print("  Option 1: Set in Windows environment variables")
    print("  Option 2: Add JIRA_API_TOKEN=your_token to .env file")
    exit(1)

# Connect to JIRA
try:
    jira = JIRA(
        server=JIRA_DOMAIN,
        basic_auth=(EMAIL, API_TOKEN)
    )
    print("✅ Successfully connected to JIRA!")
except Exception as e:
    print(f"❌ Failed to connect to JIRA: {e}")
    exit(1)

# Example: Get all projects
try:
    projects = jira.projects()
    print(f"\n📋 Found {len(projects)} projects:")
    for project in projects:
        print(f"  {project.key}: {project.name}")
except Exception as e:
    print(f"❌ Failed to get projects: {e}")

# JQL for your query - using project key is more reliable
jql_query = 'project = AIP AND status = Open'

# Search for issues
try:
    issues = jira.search_issues(jql_query, maxResults=50)  # Adjust maxResults as needed

    print(f"\n🎫 Found {len(issues)} open issues in 'API Implementation - Production':")
    for issue in issues:
        print(f"  {issue.key}: {issue.fields.summary}")
        # Optionally show more details
        # print(f"    Status: {issue.fields.status}")
        # print(f"    Assignee: {issue.fields.assignee}")
        # print(f"    Priority: {issue.fields.priority}")
except Exception as e:
    print(f"❌ Failed to search issues: {e}")