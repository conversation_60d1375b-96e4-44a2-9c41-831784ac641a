#!/usr/bin/env python3
"""
Kibana Query Test Script

This script tests the Kibana query functionality using the extracted RQUID
from JIRA tickets. Run this when you're on SCB's internal network.

Usage:
python kibana_query_test.py [RQUID]

If no RQUID is provided, it will use the one we extracted: 78148764314453026234
"""

import sys
import json
import requests
import base64
from datetime import datetime
import urllib3

# Disable SSL warnings for internal testing
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Kibana configuration
KIBANA_URL = "https://eapi.kb.ceceop.scb.co.th:9243/api/console/proxy"
KIBANA_USERNAME = "ece-eapi-ad-user"
KIBANA_PASSWORD = "tF!eSq-ugaPJ@3"
KIBANA_AUTH = base64.b64encode(f"{KIBANA_USERNAME}:{KIBANA_PASSWORD}".encode()).decode()

def query_kibana(rquid: str):
    """Query Kibana for logs related to the RQUID"""
    
    print(f"🔍 Querying Kibana for RQUID: {rquid}")
    print(f"📡 Kibana URL: {KIBANA_URL}")
    print(f"👤 Username: {KIBANA_USERNAME}")
    
    kibana_query = {
        "from": 0,
        "size": 100,
        "_source": [
            "jsonResp.RequestUID",
            "jsonResp.requestUrl",
            "jsonResp.routingUrl", 
            "jsonResp.routingCode",
            "jsonResp.serviceName",
            "jsonResp.Latency",
            "@timestamp"
        ],
        "query": {
            "bool": {
                "must": [
                    {
                        "match": {
                            "jsonResp.RequestUID": rquid
                        }
                    },
                    {
                        "match": {
                            "jsonResp.Level": "AUDIT"
                        }
                    }
                ]
            }
        }
    }
    
    headers = {
        'Content-Type': 'application/json',
        'kbn-xsrf': 'true',
        'Authorization': f'Basic {KIBANA_AUTH}'
    }
    
    print(f"\n📋 Query payload:")
    print(json.dumps(kibana_query, indent=2))
    
    try:
        print(f"\n🚀 Sending request to Kibana...")
        
        response = requests.post(
            f"{KIBANA_URL}?path=gw-prd%2F_search&method=POST",
            headers=headers,
            json=kibana_query,
            timeout=30,
            verify=False  # For internal testing only
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            kibana_data = response.json()
            hits = kibana_data.get('hits', {}).get('hits', [])
            total_hits = kibana_data.get('hits', {}).get('total', {})
            
            print(f"\n✅ Kibana query successful!")
            print(f"📈 Total hits: {total_hits}")
            print(f"📦 Records returned: {len(hits)}")
            
            if len(hits) > 0:
                print(f"\n📋 Sample records:")
                for i, record in enumerate(hits[:3]):  # Show first 3 records
                    source = record.get('_source', {})
                    json_resp = source.get('jsonResp', {})
                    
                    print(f"\n   🔸 Record {i+1}:")
                    print(f"      Request UID: {json_resp.get('RequestUID', 'N/A')}")
                    print(f"      Service Name: {json_resp.get('serviceName', 'N/A')}")
                    print(f"      Request URL: {json_resp.get('requestUrl', 'N/A')}")
                    print(f"      Routing URL: {json_resp.get('routingUrl', 'N/A')}")
                    print(f"      Routing Code: {json_resp.get('routingCode', 'N/A')}")
                    print(f"      Latency: {json_resp.get('Latency', 'N/A')}")
                    print(f"      Timestamp: {source.get('@timestamp', 'N/A')}")
            else:
                print(f"\n⚠️ No records found for RQUID: {rquid}")
                print(f"   This could mean:")
                print(f"   - The RQUID doesn't exist in the logs")
                print(f"   - The logs are outside the default time range")
                print(f"   - The RQUID format doesn't match exactly")
            
            # Save results
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"kibana_query_results_{rquid}_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(kibana_data, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Full results saved to: {filename}")
            
            return True
            
        else:
            print(f"\n❌ Kibana query failed!")
            print(f"   Status Code: {response.status_code}")
            print(f"   Response Text: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError as e:
        print(f"\n🌐 Connection Error:")
        print(f"   {str(e)}")
        print(f"\n💡 This is expected if you're not on SCB's internal network.")
        print(f"   The Kibana server is only accessible from within the corporate network.")
        return False
        
    except Exception as e:
        print(f"\n💥 Unexpected error:")
        print(f"   {str(e)}")
        return False

def generate_curl_command(rquid: str):
    """Generate the equivalent curl command for manual testing"""
    
    query_json = {
        "from": 0,
        "size": 100,
        "_source": [
            "jsonResp.RequestUID",
            "jsonResp.requestUrl",
            "jsonResp.routingUrl", 
            "jsonResp.routingCode",
            "jsonResp.serviceName",
            "jsonResp.Latency",
            "@timestamp"
        ],
        "query": {
            "bool": {
                "must": [
                    {
                        "match": {
                            "jsonResp.RequestUID": rquid
                        }
                    },
                    {
                        "match": {
                            "jsonResp.Level": "AUDIT"
                        }
                    }
                ]
            }
        }
    }
    
    curl_command = f"""curl --location --request POST 'https://eapi.kb.ceceop.scb.co.th:9243/api/console/proxy?path=gw-prd%2F_search&method=POST' \\
--header 'Content-Type: application/json' \\
--header 'kbn-xsrf: true' \\
--header 'Authorization: Basic {KIBANA_AUTH}' \\
--data-raw '{json.dumps(query_json, separators=(",", ":"))}'"""
    
    return curl_command

def main():
    """Main function"""
    
    # Default RQUID from our JIRA extraction
    default_rquid = "78148764314453026234"
    
    # Get RQUID from command line or use default
    if len(sys.argv) > 1:
        rquid = sys.argv[1]
        print(f"🎯 Using provided RQUID: {rquid}")
    else:
        rquid = default_rquid
        print(f"🎯 Using default RQUID from JIRA extraction: {rquid}")
    
    print(f"\n🔗 JIRA-Kibana Integration Test")
    print(f"=" * 50)
    print(f"RQUID: {rquid}")
    print(f"Source: {'Command line' if len(sys.argv) > 1 else 'JIRA ticket AIP-5642'}")
    
    # Test the Kibana query
    success = query_kibana(rquid)
    
    # Generate curl command for manual testing
    print(f"\n📋 Equivalent curl command:")
    print(f"=" * 50)
    curl_cmd = generate_curl_command(rquid)
    print(curl_cmd)
    
    # Save curl command to file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    curl_filename = f"kibana_curl_command_{timestamp}.sh"
    
    with open(curl_filename, 'w', encoding='utf-8') as f:
        f.write("#!/bin/bash\n")
        f.write("# Kibana query for RQUID: " + rquid + "\n")
        f.write("# Generated: " + datetime.now().isoformat() + "\n\n")
        f.write(curl_cmd)
    
    print(f"\n💾 Curl command saved to: {curl_filename}")
    
    if success:
        print(f"\n🎉 Kibana integration test completed successfully!")
    else:
        print(f"\n⚠️ Kibana query failed - likely due to network access restrictions.")
        print(f"   Try running the curl command from a machine with access to SCB's internal network.")

if __name__ == "__main__":
    main()
